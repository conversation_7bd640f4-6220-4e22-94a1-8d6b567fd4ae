# The following block will be reuse in many environment
provider "aws" {
  region = "ap-southeast-5"
  assume_role {
    role_arn = "arn:aws:iam::${var.aws_provision_id}:role/${var.aws_provision_role}"
  }
}

variable "aws_provision_id" {
  type        = string
  description = "aws account id to be use to provision"
}

variable "aws_provision_role" {
  type        = string
  description = "aws role name to be use to provision"
}

# Pin version to avoid breaking in future changes
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "> 5.0.0"
    }
  }
  required_version = ">= 1.7.5"
}

data "aws_caller_identity" "current" {}

# Display current identity
output "aws_caller_identity" {
  description = "The AWS account ID of the current identity"
  value       = data.aws_caller_identity.current
}

locals {
  this = try(yamldecode(file("config.yaml"))["this"], {})
}

# Master account
provider "aws" {
  region = "ap-southeast-5"
  alias  = "master"
}

data "aws_caller_identity" "master" {
  provider = aws.master
}

# Display current identity
output "aws_caller_identity_master" {
  description = "The AWS account ID of the current identity"
  value       = data.aws_caller_identity.master
}