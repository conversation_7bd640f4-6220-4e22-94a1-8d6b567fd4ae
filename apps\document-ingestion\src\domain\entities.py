"""
Domain Entities for Document Ingestion Pipeline

This module contains the core business entities that represent the fundamental
concepts in the document ingestion domain. These entities encapsulate business
rules and maintain data integrity.

All entities follow Domain-Driven Design principles:
- Value Objects: Immutable objects that represent concepts
- Entities: Objects with identity that can change over time
- Aggregate Roots: Entities that control access to their aggregates
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Union
import uuid


class DocumentStatus(Enum):
    """Document processing status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessingStage(Enum):
    """Processing stage enumeration"""
    UPLOADED = "uploaded"
    VALIDATING = "validating"
    PARSING = "parsing"
    GENERATING_EMBEDDINGS = "generating_embeddings"
    STORING = "storing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class UserId:
    """Value object for user identification"""
    value: str
    
    def __post_init__(self):
        if not self.value or not isinstance(self.value, str):
            raise ValueError("User ID must be a non-empty string")
    
    def __str__(self) -> str:
        return self.value


@dataclass
class DocumentId:
    """Value object for document identification"""
    value: str
    
    def __post_init__(self):
        if not self.value or not isinstance(self.value, str):
            raise ValueError("Document ID must be a non-empty string")
    
    @classmethod
    def generate(cls) -> 'DocumentId':
        """Generate a new unique document ID"""
        return cls(str(uuid.uuid4()))
    
    def __str__(self) -> str:
        return self.value


@dataclass
class S3Location:
    """Value object for S3 file location"""
    bucket: str
    key: str
    
    def __post_init__(self):
        if not self.bucket or not self.key:
            raise ValueError("Bucket and key must be non-empty strings")
    
    @property
    def uri(self) -> str:
        """Get S3 URI representation"""
        return f"s3://{self.bucket}/{self.key}"


@dataclass
class ProductData:
    """Value object for product information"""
    data: Dict[str, Any]
    row_index: int
    
    def __post_init__(self):
        if not isinstance(self.data, dict):
            raise ValueError("Product data must be a dictionary")
        if self.row_index < 0:
            raise ValueError("Row index must be non-negative")
    
    def get_searchable_text(self, search_fields: List[str]) -> str:
        """Extract searchable text from product data"""
        text_parts = []
        
        for field in search_fields:
            value = None
            for key in self.data.keys():
                if key.lower() == field.lower():
                    value = self.data[key]
                    break
            
            if value is not None and str(value).strip():
                field_name = field.replace("_", " ").title()
                text_parts.append(f"{field_name}: {str(value).strip()}")
        
        # If no specific fields found, include all non-empty string fields
        if not text_parts:
            for key, value in self.data.items():
                if isinstance(value, str) and value.strip():
                    field_name = key.replace("_", " ").title()
                    text_parts.append(f"{field_name}: {value.strip()}")
        
        return " | ".join(text_parts) if text_parts else "No searchable content"


@dataclass
class EmbeddingVector:
    """Value object for embedding vector"""
    vector: List[float]
    dimensions: int
    model: str
    
    def __post_init__(self):
        if not self.vector or len(self.vector) != self.dimensions:
            raise ValueError(f"Vector must have exactly {self.dimensions} dimensions")
        if not all(isinstance(x, (int, float)) for x in self.vector):
            raise ValueError("Vector must contain only numeric values")


@dataclass
class Document:
    """Document aggregate root"""
    id: DocumentId
    user_id: UserId
    s3_location: S3Location
    original_filename: str
    status: DocumentStatus = DocumentStatus.PENDING
    processing_stage: ProcessingStage = ProcessingStage.UPLOADED
    column_names: List[str] = field(default_factory=list)
    total_rows: int = 0
    processed_rows: int = 0
    error_message: Optional[str] = None
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    processing_started_at: Optional[datetime] = None
    processing_completed_at: Optional[datetime] = None
    
    def start_processing(self) -> None:
        """Mark document as processing"""
        self.status = DocumentStatus.PROCESSING
        self.processing_stage = ProcessingStage.VALIDATING
        self.processing_started_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_stage(self, stage: ProcessingStage) -> None:
        """Update processing stage"""
        self.processing_stage = stage
        self.updated_at = datetime.now(timezone.utc)
    
    def complete_processing(self, processed_rows: int) -> None:
        """Mark document as completed"""
        self.status = DocumentStatus.COMPLETED
        self.processing_stage = ProcessingStage.COMPLETED
        self.processed_rows = processed_rows
        self.processing_completed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def fail_processing(self, error_message: str) -> None:
        """Mark document as failed"""
        self.status = DocumentStatus.FAILED
        self.processing_stage = ProcessingStage.FAILED
        self.error_message = error_message
        self.processing_completed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_csv_metadata(self, column_names: List[str], total_rows: int) -> None:
        """Update CSV metadata after parsing"""
        self.column_names = column_names
        self.total_rows = total_rows
        self.updated_at = datetime.now(timezone.utc)


@dataclass
class ProductEmbedding:
    """Product embedding entity"""
    document_id: DocumentId
    user_id: UserId
    row_index: int
    product_data: ProductData
    searchable_text: str
    embedding_vector: EmbeddingVector
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        if self.row_index != self.product_data.row_index:
            raise ValueError("Row index must match product data row index")


@dataclass
class ProcessingResult:
    """Result of document processing operation"""
    success: bool
    document_id: DocumentId
    user_id: UserId
    processed_rows: int = 0
    error_message: Optional[str] = None
    processing_time_ms: float = 0
    
    @classmethod
    def success_result(cls, document_id: DocumentId, user_id: UserId, 
                      processed_rows: int, processing_time_ms: float) -> 'ProcessingResult':
        """Create a successful processing result"""
        return cls(
            success=True,
            document_id=document_id,
            user_id=user_id,
            processed_rows=processed_rows,
            processing_time_ms=processing_time_ms
        )
    
    @classmethod
    def failure_result(cls, document_id: DocumentId, user_id: UserId, 
                      error_message: str, processing_time_ms: float) -> 'ProcessingResult':
        """Create a failed processing result"""
        return cls(
            success=False,
            document_id=document_id,
            user_id=user_id,
            error_message=error_message,
            processing_time_ms=processing_time_ms
        )
