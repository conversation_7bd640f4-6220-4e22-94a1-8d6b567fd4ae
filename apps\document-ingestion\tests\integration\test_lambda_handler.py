"""
Integration tests for Lambda handler

These tests verify the complete Lambda handler flow with real
dependency injection container but mocked external services.
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock

from src.presentation.lambda_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, lambda_handler
from src.infrastructure.dependency_injection import ApplicationContainer


class TestLambdaHandlerIntegration:
    """Integration tests for Lambda handler"""
    
    @pytest.fixture
    def lambda_context(self):
        """Mock Lambda context"""
        context = Mock()
        context.aws_request_id = "test-request-123"
        context.function_name = "document-ingestion-test"
        context.get_remaining_time_in_millis.return_value = 300000
        return context
    
    @pytest.fixture
    def s3_event(self):
        """Sample S3 event"""
        return {
            "Records": [
                {
                    "eventSource": "aws:s3",
                    "s3": {
                        "bucket": {"name": "test-bucket"},
                        "object": {"key": "user123/uploaded/products.csv"}
                    }
                }
            ]
        }
    
    @pytest.fixture
    def api_gateway_event(self):
        """Sample API Gateway event"""
        return {
            "httpMethod": "GET",
            "path": "/document-ingestion/health",
            "headers": {},
            "body": None
        }
    
    @pytest.mark.integration
    def test_lambda_handler_api_gateway(self, api_gateway_event, lambda_context):
        """Test Lambda handler with API Gateway event"""
        response = lambda_handler(api_gateway_event, lambda_context)
        
        assert response["statusCode"] == 200
        assert "Content-Type" in response["headers"]
        
        body = json.loads(response["body"])
        assert body["message"] == "Document ingestion Lambda is running"
        assert body["status"] == "healthy"
        assert body["event_type"] == "api_gateway"
    
    @pytest.mark.integration
    @patch('src.infrastructure.repositories.SupabaseDocumentRepository')
    @patch('src.infrastructure.repositories.SupabaseProductEmbeddingRepository')
    @patch('src.infrastructure.storage.S3FileStorage')
    @patch('src.infrastructure.services.RetryableOpenAIEmbeddingService')
    def test_lambda_handler_s3_event_success(
        self, mock_embedding_service, mock_storage, mock_embedding_repo, 
        mock_doc_repo, s3_event, lambda_context
    ):
        """Test Lambda handler with successful S3 event processing"""
        # Setup mocks for successful processing
        mock_storage_instance = Mock()
        mock_storage_instance.extract_user_info.return_value = (
            Mock(value="user123"), {"filename": "products.csv"}
        )
        mock_storage_instance.validate_file_format.return_value = True
        mock_storage_instance.download_content.return_value = b"name,price\nProduct1,29.99"
        mock_storage_instance.move_file.return_value = True
        mock_storage.return_value = mock_storage_instance
        
        # Mock CSV processor
        with patch('src.infrastructure.processors.EnhancedCSVProcessor') as mock_csv:
            mock_csv_instance = Mock()
            mock_csv_instance.parse_csv.return_value = (
                ["name", "price"],
                [Mock(data={"name": "Product1", "price": "29.99"}, row_index=0)]
            )
            mock_csv_instance.clean_csv_data.side_effect = lambda x: x
            mock_csv_instance.validate_csv_data.return_value = True
            mock_csv.return_value = mock_csv_instance
            
            # Mock embedding generation
            with patch('src.domain.services.EmbeddingGenerationService') as mock_embed_gen:
                mock_embed_gen_instance = Mock()
                mock_embed_gen_instance.generate_product_embeddings.return_value = [
                    {
                        'row_index': 0,
                        'product_data': {"name": "Product1", "price": "29.99"},
                        'searchable_text': "Name: Product1",
                        'embedding_vector': [0.1, 0.2, 0.3]
                    }
                ]
                mock_embed_gen.return_value = mock_embed_gen_instance
                
                # Mock repositories
                mock_doc_repo_instance = Mock()
                mock_doc_repo_instance.save = AsyncMock()
                mock_doc_repo.return_value = mock_doc_repo_instance
                
                mock_embedding_repo_instance = Mock()
                mock_embedding_repo_instance.save_batch = AsyncMock()
                mock_embedding_repo.return_value = mock_embedding_repo_instance
                
                # Execute
                response = lambda_handler(s3_event, lambda_context)
                
                # Verify response
                assert response["statusCode"] == 200
                
                body = json.loads(response["body"])
                assert body["message"] == "S3 processing completed"
                assert "results" in body
                assert "architecture" in body
    
    @pytest.mark.integration
    @patch('src.infrastructure.storage.S3FileStorage')
    def test_lambda_handler_s3_event_failure(
        self, mock_storage, s3_event, lambda_context
    ):
        """Test Lambda handler with failed S3 event processing"""
        # Setup mocks for failure
        mock_storage_instance = Mock()
        mock_storage_instance.extract_user_info.return_value = (None, None)
        mock_storage.return_value = mock_storage_instance
        
        # Execute
        response = lambda_handler(s3_event, lambda_context)
        
        # Verify response
        assert response["statusCode"] == 200
        
        body = json.loads(response["body"])
        assert body["message"] == "S3 processing completed"
        assert "results" in body
        
        # Check that the result indicates failure
        results = body["results"]
        assert len(results) == 1
        assert results[0]["success"] is False
    
    @pytest.mark.integration
    def test_lambda_handler_graceful_error_handling(self, lambda_context):
        """Test Lambda handler gracefully handles S3 record processing errors"""
        # Create an event that will cause an exception during S3 processing
        invalid_event = {
            "Records": [
                {
                    "eventSource": "aws:s3",
                    "s3": {
                        "bucket": {"name": "test-bucket"},
                        "object": {"key": None}  # This will cause an error
                    }
                }
            ]
        }

        response = lambda_handler(invalid_event, lambda_context)

        # The handler should gracefully handle the error and return 200
        # but with 0 successful records
        assert response["statusCode"] == 200

        body = json.loads(response["body"])
        assert body["message"] == "S3 processing completed"
        assert "results" in body
        assert len(body["results"]) == 1  # One record processed
        assert body["results"][0]["success"] is False  # But it failed
    
    @pytest.mark.integration
    def test_dependency_injection_wiring(self):
        """Test that dependency injection wiring works in Lambda handler"""
        # Create container and wire it
        container = ApplicationContainer()
        container.wire(modules=['src.presentation.lambda_handler'])
        
        # Create handler - this should work with dependency injection
        handler = LambdaHandler()
        
        # Verify handler has dependencies injected
        assert handler._use_case is not None
        assert handler._logger is not None
        assert handler._metrics is not None
    
    @pytest.mark.integration
    def test_container_singleton_behavior_in_lambda(self):
        """Test that container maintains singleton behavior across Lambda calls"""
        # Simulate multiple Lambda invocations
        context = Mock()
        context.aws_request_id = "test-request-1"
        context.function_name = "test-function"
        context.get_remaining_time_in_millis.return_value = 300000
        
        event = {"httpMethod": "GET", "path": "/document-ingestion/health"}
        
        # First call
        response1 = lambda_handler(event, context)
        
        # Second call
        context.aws_request_id = "test-request-2"
        response2 = lambda_handler(event, context)
        
        # Both should succeed
        assert response1["statusCode"] == 200
        assert response2["statusCode"] == 200
        
        # Verify responses are consistent
        body1 = json.loads(response1["body"])
        body2 = json.loads(response2["body"])
        
        assert body1["message"] == body2["message"]
        assert body1["status"] == body2["status"]


class TestLambdaHandlerClass:
    """Test LambdaHandler class directly"""
    
    @pytest.mark.integration
    def test_handler_creation_with_di(self):
        """Test creating handler with dependency injection"""
        container = ApplicationContainer()
        container.wire(modules=['src.presentation.lambda_handler'])
        
        handler = LambdaHandler()
        
        assert handler is not None
        assert hasattr(handler, '_use_case')
        assert hasattr(handler, '_logger')
        assert hasattr(handler, '_metrics')
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_handle_api_gateway_request(self):
        """Test handling API Gateway request directly"""
        container = ApplicationContainer()
        container.wire(modules=['src.presentation.lambda_handler'])
        
        handler = LambdaHandler()
        
        event = {"httpMethod": "GET", "path": "/document-ingestion/health"}
        context = Mock()
        context.aws_request_id = "test-request"
        context.function_name = "test-function"
        context.get_remaining_time_in_millis.return_value = 300000
        
        response = await handler.handle(event, context)
        
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["status"] == "healthy"
