"""
Pytest configuration and shared fixtures for document ingestion tests

This module provides shared fixtures and configuration for all tests,
including dependency injection container setup and mock objects.
"""

import os
import pytest
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, List
from pathlib import Path
import sys

# Add src to Python path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.dependency_injection import ApplicationContainer
from src.domain.entities import (
    Document, DocumentId, UserId, S3Location, ProductData, 
    EmbeddingVector, ProductEmbedding, ProcessingResult
)
from src.domain.interfaces import (
    IDocumentRepository, IProductEmbeddingRepository, IFileStorage,
    IEmbeddingService, ICSVProcessor, IEventPublisher, ILogger,
    IMetricsCollector, IConfiguration
)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment variables"""
    os.environ.update({
        'OPENAI_API_KEY': 'test-openai-key-12345',
        'SUPABASE_URL': 'https://test-project.supabase.co',
        'SUPABASE_SERVICE_ROLE_KEY': 'test-service-key-12345',
        'ENVIRONMENT': 'test',
        'LOG_LEVEL': 'DEBUG',
        'MAX_FILE_SIZE_BYTES': '1048576',  # 1MB for tests
        'MAX_ROWS': '1000',
        'BATCH_SIZE': '10',
        'EMBEDDING_BATCH_SIZE': '5',
        'SUPABASE_BATCH_SIZE': '5'
    })


@pytest.fixture
def mock_configuration():
    """Mock configuration for testing"""
    config = Mock(spec=IConfiguration)
    config.get_openai_api_key.return_value = 'test-openai-key'
    config.get_openai_model.return_value = 'text-embedding-3-small'
    config.get_openai_dimensions.return_value = 512
    config.get_supabase_url.return_value = 'https://test.supabase.co'
    config.get_supabase_service_key.return_value = 'test-service-key'
    config.get_batch_size.return_value = 10
    config.get_max_file_size.return_value = 1048576
    config.get_max_rows.return_value = 1000
    config.get_retry_attempts.return_value = 3
    config.get_retry_min_wait.return_value = 1
    config.get_retry_max_wait.return_value = 10
    config.get_embedding_batch_size.return_value = 5
    config.get_supabase_batch_size.return_value = 5
    config.get_similarity_threshold.return_value = 0.7
    config.get_max_search_results.return_value = 10
    config.is_production.return_value = False
    config.is_development.return_value = True
    return config


@pytest.fixture
def mock_logger():
    """Mock logger for testing"""
    logger = Mock(spec=ILogger)
    return logger


@pytest.fixture
def mock_metrics():
    """Mock metrics collector for testing"""
    metrics = Mock(spec=IMetricsCollector)
    return metrics


@pytest.fixture
def mock_document_repository():
    """Mock document repository for testing"""
    repo = Mock(spec=IDocumentRepository)
    return repo


@pytest.fixture
def mock_embedding_repository():
    """Mock embedding repository for testing"""
    repo = Mock(spec=IProductEmbeddingRepository)
    return repo


@pytest.fixture
def mock_file_storage():
    """Mock file storage for testing"""
    storage = Mock(spec=IFileStorage)
    return storage


@pytest.fixture
def mock_embedding_service():
    """Mock embedding service for testing"""
    service = Mock(spec=IEmbeddingService)
    service.get_embedding_config.return_value = {
        'model': 'text-embedding-3-small',
        'dimensions': 512
    }
    return service


@pytest.fixture
def mock_csv_processor():
    """Mock CSV processor for testing"""
    processor = Mock(spec=ICSVProcessor)
    return processor


@pytest.fixture
def mock_event_publisher():
    """Mock event publisher for testing"""
    publisher = Mock(spec=IEventPublisher)
    return publisher


@pytest.fixture
def mock_validation_service():
    """Mock validation service for testing"""
    from src.domain.services import DocumentValidationService
    service = Mock(spec=DocumentValidationService)
    service.validate_file_size.return_value = True
    service.validate_row_count.return_value = True
    service.validate_column_names.return_value = True
    service.calculate_data_quality_score.return_value = 0.8
    return service


@pytest.fixture
def mock_security_service():
    """Mock security service for testing"""
    from src.domain.services import MultiTenantSecurityService
    service = Mock(spec=MultiTenantSecurityService)
    service.validate_tenant_access.return_value = True
    service.sanitize_user_id.side_effect = lambda x: x
    return service


@pytest.fixture
def mock_embedding_generation_service():
    """Mock embedding generation service for testing"""
    from src.domain.services import EmbeddingGenerationService
    service = Mock(spec=EmbeddingGenerationService)
    service.generate_product_embeddings.return_value = [
        {
            'row_index': 0,
            'product_data': {"name": "Test Product", "price": "29.99"},
            'searchable_text': "Name: Test Product",
            'embedding_vector': [0.1, 0.2, 0.3]
        }
    ]
    return service


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing"""
    return UserId("test-user-123")


@pytest.fixture
def sample_document_id():
    """Sample document ID for testing"""
    return DocumentId("test-doc-456")


@pytest.fixture
def sample_s3_location():
    """Sample S3 location for testing"""
    return S3Location(bucket="test-bucket", key="test-user-123/uploaded/products.csv")


@pytest.fixture
def sample_document(sample_document_id, sample_user_id, sample_s3_location):
    """Sample document entity for testing"""
    return Document(
        id=sample_document_id,
        user_id=sample_user_id,
        s3_location=sample_s3_location,
        original_filename="products.csv"
    )


@pytest.fixture
def sample_product_data():
    """Sample product data for testing"""
    return ProductData(
        data={
            "name": "Test Product",
            "description": "A test product for unit testing",
            "price": "29.99",
            "category": "Electronics"
        },
        row_index=0
    )


@pytest.fixture
def sample_embedding_vector():
    """Sample embedding vector for testing"""
    return EmbeddingVector(
        vector=[0.1, 0.2, 0.3, 0.4, 0.5] * 102 + [0.1, 0.2],  # 512 dimensions
        dimensions=512,
        model="text-embedding-3-small"
    )


@pytest.fixture
def sample_product_embedding(sample_document_id, sample_user_id, sample_product_data, sample_embedding_vector):
    """Sample product embedding for testing"""
    return ProductEmbedding(
        document_id=sample_document_id,
        user_id=sample_user_id,
        row_index=0,
        product_data=sample_product_data,
        searchable_text="Name: Test Product | Description: A test product for unit testing",
        embedding_vector=sample_embedding_vector
    )


@pytest.fixture
def sample_csv_content():
    """Sample CSV content for testing"""
    return b"""name,description,price,category
Test Product 1,A great product,29.99,Electronics
Test Product 2,Another product,39.99,Home
Test Product 3,Third product,19.99,Books"""


@pytest.fixture
def sample_s3_event():
    """Sample S3 event for testing"""
    return {
        "Records": [
            {
                "eventSource": "aws:s3",
                "s3": {
                    "bucket": {"name": "test-bucket"},
                    "object": {"key": "test-user-123/uploaded/products.csv"}
                }
            }
        ]
    }


@pytest.fixture
def sample_lambda_context():
    """Sample Lambda context for testing"""
    context = Mock()
    context.aws_request_id = "test-request-123"
    context.function_name = "document-ingestion-test"
    context.get_remaining_time_in_millis.return_value = 300000  # 5 minutes
    return context


@pytest.fixture
def dependency_container():
    """Real dependency injection container for integration tests"""
    container = ApplicationContainer()
    return container


@pytest.fixture
def mocked_container(
    mock_configuration, mock_logger, mock_metrics,
    mock_document_repository, mock_embedding_repository,
    mock_file_storage, mock_embedding_service,
    mock_csv_processor, mock_event_publisher,
    mock_validation_service, mock_security_service,
    mock_embedding_generation_service
):
    """Dependency injection container with all mocked dependencies"""
    container = ApplicationContainer()

    # Override providers with mocks
    container.config.override(mock_configuration)
    container.logger.override(mock_logger)
    container.metrics_collector.override(mock_metrics)
    container.document_repository.override(mock_document_repository)
    container.embedding_repository.override(mock_embedding_repository)
    container.file_storage.override(mock_file_storage)
    container.embedding_service.override(mock_embedding_service)
    container.csv_processor.override(mock_csv_processor)
    container.event_publisher.override(mock_event_publisher)
    container.document_validation_service.override(mock_validation_service)
    container.security_service.override(mock_security_service)
    container.embedding_generation_service.override(mock_embedding_generation_service)

    yield container

    # Reset overrides after test - only if they exist
    try:
        container.reset_last_overriding()
    except Exception:
        # If reset fails, manually reset each override
        container.config.reset_override()
        container.logger.reset_override()
        container.metrics_collector.reset_override()
        container.document_repository.reset_override()
        container.embedding_repository.reset_override()
        container.file_storage.reset_override()
        container.embedding_service.reset_override()
        container.csv_processor.reset_override()
        container.event_publisher.reset_override()
        container.document_validation_service.reset_override()
        container.security_service.reset_override()
        container.embedding_generation_service.reset_override()


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test (fast, isolated)"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test (slower, with real dependencies)"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location"""
    for item in items:
        # Mark tests in unit/ directory as unit tests
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        # Mark tests in integration/ directory as integration tests
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
