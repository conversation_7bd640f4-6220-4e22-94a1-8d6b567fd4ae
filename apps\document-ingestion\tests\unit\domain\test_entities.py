"""
Unit tests for domain entities

These tests verify the business logic and invariants of domain entities
without any external dependencies.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock
from freezegun import freeze_time

from src.domain.entities import (
    Document, DocumentId, UserId, S3Location, ProductData,
    EmbeddingVector, ProductEmbedding, ProcessingResult,
    DocumentStatus, ProcessingStage
)


class TestUserId:
    """Test UserId value object"""
    
    def test_valid_user_id(self):
        """Test creating a valid user ID"""
        user_id = UserId("test-user-123")
        assert str(user_id) == "test-user-123"
        assert user_id.value == "test-user-123"
    
    def test_invalid_user_id_empty(self):
        """Test that empty user ID raises ValueError"""
        with pytest.raises(ValueError, match="User ID must be a non-empty string"):
            UserId("")
    
    def test_invalid_user_id_none(self):
        """Test that None user ID raises ValueError"""
        with pytest.raises(ValueError, match="User ID must be a non-empty string"):
            UserId(None)


class TestDocumentId:
    """Test DocumentId value object"""
    
    def test_valid_document_id(self):
        """Test creating a valid document ID"""
        doc_id = DocumentId("test-doc-456")
        assert str(doc_id) == "test-doc-456"
        assert doc_id.value == "test-doc-456"
    
    def test_generate_document_id(self):
        """Test generating a new document ID"""
        doc_id = DocumentId.generate()
        assert isinstance(doc_id, DocumentId)
        assert len(str(doc_id)) == 36  # UUID length
    
    def test_invalid_document_id_empty(self):
        """Test that empty document ID raises ValueError"""
        with pytest.raises(ValueError, match="Document ID must be a non-empty string"):
            DocumentId("")


class TestS3Location:
    """Test S3Location value object"""
    
    def test_valid_s3_location(self):
        """Test creating a valid S3 location"""
        location = S3Location(bucket="test-bucket", key="test/file.csv")
        assert location.bucket == "test-bucket"
        assert location.key == "test/file.csv"
        assert location.uri == "s3://test-bucket/test/file.csv"
    
    def test_invalid_s3_location_empty_bucket(self):
        """Test that empty bucket raises ValueError"""
        with pytest.raises(ValueError, match="Bucket and key must be non-empty strings"):
            S3Location(bucket="", key="test/file.csv")
    
    def test_invalid_s3_location_empty_key(self):
        """Test that empty key raises ValueError"""
        with pytest.raises(ValueError, match="Bucket and key must be non-empty strings"):
            S3Location(bucket="test-bucket", key="")


class TestProductData:
    """Test ProductData value object"""
    
    def test_valid_product_data(self):
        """Test creating valid product data"""
        data = {"name": "Test Product", "price": "29.99"}
        product = ProductData(data=data, row_index=0)
        assert product.data == data
        assert product.row_index == 0
    
    def test_get_searchable_text(self):
        """Test generating searchable text from product data"""
        data = {
            "name": "Test Product",
            "description": "A great product",
            "price": "29.99"
        }
        product = ProductData(data=data, row_index=0)
        
        search_fields = ["name", "description"]
        text = product.get_searchable_text(search_fields)
        
        assert "Name: Test Product" in text
        assert "Description: A great product" in text
        assert "price" not in text.lower()
    
    def test_get_searchable_text_no_fields_found(self):
        """Test searchable text when no specific fields are found"""
        data = {"custom_field": "Custom Value", "another": "Another Value"}
        product = ProductData(data=data, row_index=0)
        
        search_fields = ["name", "description"]
        text = product.get_searchable_text(search_fields)
        
        # Should include all string fields when no specific fields found
        assert "Custom Field: Custom Value" in text
        assert "Another: Another Value" in text
    
    def test_invalid_product_data_not_dict(self):
        """Test that non-dict data raises ValueError"""
        with pytest.raises(ValueError, match="Product data must be a dictionary"):
            ProductData(data="not a dict", row_index=0)
    
    def test_invalid_product_data_negative_row_index(self):
        """Test that negative row index raises ValueError"""
        with pytest.raises(ValueError, match="Row index must be non-negative"):
            ProductData(data={}, row_index=-1)


class TestEmbeddingVector:
    """Test EmbeddingVector value object"""
    
    def test_valid_embedding_vector(self):
        """Test creating a valid embedding vector"""
        vector = [0.1, 0.2, 0.3, 0.4, 0.5]
        embedding = EmbeddingVector(
            vector=vector,
            dimensions=5,
            model="text-embedding-3-small"
        )
        assert embedding.vector == vector
        assert embedding.dimensions == 5
        assert embedding.model == "text-embedding-3-small"
    
    def test_invalid_embedding_vector_wrong_dimensions(self):
        """Test that wrong dimensions raises ValueError"""
        vector = [0.1, 0.2, 0.3]
        with pytest.raises(ValueError, match="Vector must have exactly 5 dimensions"):
            EmbeddingVector(vector=vector, dimensions=5, model="test")
    
    def test_invalid_embedding_vector_non_numeric(self):
        """Test that non-numeric values raise ValueError"""
        vector = [0.1, "invalid", 0.3]
        with pytest.raises(ValueError, match="Vector must contain only numeric values"):
            EmbeddingVector(vector=vector, dimensions=3, model="test")


class TestDocument:
    """Test Document aggregate root"""
    
    def test_create_document(self, sample_document_id, sample_user_id, sample_s3_location):
        """Test creating a document"""
        document = Document(
            id=sample_document_id,
            user_id=sample_user_id,
            s3_location=sample_s3_location,
            original_filename="test.csv"
        )
        
        assert document.id == sample_document_id
        assert document.user_id == sample_user_id
        assert document.s3_location == sample_s3_location
        assert document.original_filename == "test.csv"
        assert document.status == DocumentStatus.PENDING
        assert document.processing_stage == ProcessingStage.UPLOADED
    
    def test_start_processing(self, sample_document):
        """Test starting document processing"""
        from datetime import datetime, timezone

        # Use freezegun to advance time slightly
        with freeze_time("2025-01-01 12:00:00") as frozen_time:
            # Set the original time with timezone
            original_time = datetime.now(timezone.utc)
            sample_document.updated_at = original_time

            # Advance time by 1 second
            frozen_time.tick(delta=1)

            sample_document.start_processing()

            assert sample_document.status == DocumentStatus.PROCESSING
            assert sample_document.processing_stage == ProcessingStage.VALIDATING
            assert sample_document.processing_started_at is not None
            assert sample_document.updated_at > original_time
    
    def test_update_stage(self, sample_document):
        """Test updating processing stage"""
        from datetime import datetime, timezone

        with freeze_time("2025-01-01 12:00:00") as frozen_time:
            # Set the original time with timezone
            original_time = datetime.now(timezone.utc)
            sample_document.updated_at = original_time

            # Advance time by 1 second
            frozen_time.tick(delta=1)

            sample_document.update_stage(ProcessingStage.PARSING)

            assert sample_document.processing_stage == ProcessingStage.PARSING
            assert sample_document.updated_at > original_time
    
    def test_complete_processing(self, sample_document):
        """Test completing document processing"""
        sample_document.complete_processing(100)
        
        assert sample_document.status == DocumentStatus.COMPLETED
        assert sample_document.processing_stage == ProcessingStage.COMPLETED
        assert sample_document.processed_rows == 100
        assert sample_document.processing_completed_at is not None
    
    def test_fail_processing(self, sample_document):
        """Test failing document processing"""
        error_message = "Test error"
        
        sample_document.fail_processing(error_message)
        
        assert sample_document.status == DocumentStatus.FAILED
        assert sample_document.processing_stage == ProcessingStage.FAILED
        assert sample_document.error_message == error_message
        assert sample_document.processing_completed_at is not None
    
    def test_update_csv_metadata(self, sample_document):
        """Test updating CSV metadata"""
        columns = ["name", "price", "description"]
        total_rows = 150
        
        sample_document.update_csv_metadata(columns, total_rows)
        
        assert sample_document.column_names == columns
        assert sample_document.total_rows == total_rows


class TestProcessingResult:
    """Test ProcessingResult value object"""
    
    def test_success_result(self, sample_document_id, sample_user_id):
        """Test creating a success result"""
        result = ProcessingResult.success_result(
            document_id=sample_document_id,
            user_id=sample_user_id,
            processed_rows=100,
            processing_time_ms=5000.0
        )
        
        assert result.success is True
        assert result.document_id == sample_document_id
        assert result.user_id == sample_user_id
        assert result.processed_rows == 100
        assert result.processing_time_ms == 5000.0
        assert result.error_message is None
    
    def test_failure_result(self, sample_document_id, sample_user_id):
        """Test creating a failure result"""
        error_message = "Processing failed"
        
        result = ProcessingResult.failure_result(
            document_id=sample_document_id,
            user_id=sample_user_id,
            error_message=error_message,
            processing_time_ms=2000.0
        )
        
        assert result.success is False
        assert result.document_id == sample_document_id
        assert result.user_id == sample_user_id
        assert result.error_message == error_message
        assert result.processing_time_ms == 2000.0
        assert result.processed_rows == 0
