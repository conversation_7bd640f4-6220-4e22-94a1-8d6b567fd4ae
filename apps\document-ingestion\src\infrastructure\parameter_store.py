"""
AWS Systems Manager Parameter Store Integration for Document Ingestion Pipeline

This module provides secure access to parameters stored in AWS SSM Parameter Store,
with fallback to environment variables for local development.
"""

import os
import json
import boto3
from typing import Optional, Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError


class ParameterStoreManager:
    """AWS SSM Parameter Store client with fallback to environment variables"""
    
    def __init__(self):
        self._client = None
        self._cache: Dict[str, Any] = {}
        self._is_aws_environment = self._detect_aws_environment()
    
    def _detect_aws_environment(self) -> bool:
        """Detect if running in AWS environment (Lambda, EC2, etc.)"""
        # Check for AWS Lambda environment
        if os.getenv('AWS_LAMBDA_FUNCTION_NAME'):
            return True
        
        # Check for AWS execution environment
        if os.getenv('AWS_EXECUTION_ENV'):
            return True
        
        # Check for explicit AWS credentials
        if os.getenv('AWS_ACCESS_KEY_ID') and os.getenv('AWS_SECRET_ACCESS_KEY'):
            return True
        
        # Check for LocalStack
        if os.getenv('AWS_ENDPOINT_URL'):
            return True
        
        return False
    
    @property
    def client(self):
        """Lazy initialization of AWS SSM Parameter Store client"""
        if self._client is None:
            try:
                # Configure client for LocalStack or AWS
                endpoint_url = os.getenv('AWS_ENDPOINT_URL')
                region = os.getenv('AWS_REGION', os.getenv('AWS_DEFAULT_REGION', 'us-east-1'))
                
                if endpoint_url:
                    # LocalStack configuration
                    self._client = boto3.client(
                        'ssm',
                        endpoint_url=endpoint_url,
                        region_name=region,
                        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID', 'test'),
                        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY', 'test')
                    )
                else:
                    # AWS configuration (uses IAM roles in Lambda)
                    self._client = boto3.client('ssm', region_name=region)
                    
            except (NoCredentialsError, ClientError) as e:
                print(f"Warning: Could not initialize AWS SSM Parameter Store client: {e}")
                self._client = None
        
        return self._client
    
    def _convert_legacy_secret_name_to_parameter_name(self, secret_name: str) -> str:
        """
        Convert legacy secret names to SSM parameter names
        
        Args:
            secret_name: Legacy secret name (e.g., 'lambda/openai-api-key')
            
        Returns:
            SSM parameter name (e.g., '/ezychat/document-ingestion/openai/api-key')
        """
        # Mapping of legacy secret names to SSM parameter names
        legacy_to_ssm_mapping = {
            'lambda/openai-api-key': '/ezychat/document-ingestion/openai/api-key',
            'lambda/supabase-url': '/ezychat/document-ingestion/supabase/url',
            'lambda/supabase-service-role-key': '/ezychat/document-ingestion/supabase/service-role-key',
            'lambda/supabase-anon-key': '/ezychat/document-ingestion/supabase/anon-key'
        }
        
        return legacy_to_ssm_mapping.get(secret_name, secret_name)

    def get_parameter(self, parameter_name: str, fallback_env_var: Optional[str] = None) -> Optional[str]:
        """
        Get parameter value from AWS SSM Parameter Store with fallback to environment variable
        
        Args:
            parameter_name: Name of the parameter in AWS SSM Parameter Store
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or None if not found
        """
        # Convert legacy secret name to parameter name if needed
        ssm_parameter_name = self._convert_legacy_secret_name_to_parameter_name(parameter_name)
        
        # Check cache first
        if ssm_parameter_name in self._cache:
            return self._cache[ssm_parameter_name]
        
        # Try AWS SSM Parameter Store if in AWS environment
        if self._is_aws_environment and self.client:
            try:
                response = self.client.get_parameter(
                    Name=ssm_parameter_name,
                    WithDecryption=True  # Decrypt SecureString parameters
                )
                parameter_value = response['Parameter']['Value']
                
                # Try to parse as JSON (for complex parameters)
                try:
                    parameter_data = json.loads(parameter_value)
                    # If it's a JSON object, cache the whole object
                    self._cache[ssm_parameter_name] = parameter_data
                    return parameter_data
                except json.JSONDecodeError:
                    # It's a plain string parameter
                    self._cache[ssm_parameter_name] = parameter_value
                    return parameter_value
                    
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == 'ParameterNotFound':
                    print(f"Parameter {ssm_parameter_name} not found in AWS SSM Parameter Store")
                else:
                    print(f"Error retrieving parameter {ssm_parameter_name}: {e}")
        
        # Fallback to environment variable
        if fallback_env_var:
            env_value = os.getenv(fallback_env_var)
            if env_value:
                self._cache[ssm_parameter_name] = env_value
                return env_value
        
        return None

    def get_secret(self, secret_name: str, fallback_env_var: Optional[str] = None) -> Optional[str]:
        """
        Get parameter value (backward compatibility method)
        
        Args:
            secret_name: Name of the parameter (legacy secret name will be converted)
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or None if not found
        """
        return self.get_parameter(secret_name, fallback_env_var)
    
    def get_parameter_string(self, parameter_name: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get parameter as string with empty string fallback
        
        Args:
            parameter_name: Name of the parameter in AWS SSM Parameter Store
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or empty string if not found
        """
        value = self.get_parameter(parameter_name, fallback_env_var)
        return value if isinstance(value, str) else ''

    def get_secret_string(self, secret_name: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get parameter as string (backward compatibility method)
        
        Args:
            secret_name: Name of the parameter (legacy secret name will be converted)
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or empty string if not found
        """
        return self.get_parameter_string(secret_name, fallback_env_var)

    def get_parameter_json(self, parameter_name: str, key: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get specific key from JSON parameter
        
        Args:
            parameter_name: Name of the parameter in AWS SSM Parameter Store
            key: Key within the JSON parameter
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or empty string if not found
        """
        parameter_data = self.get_parameter(parameter_name, fallback_env_var)
        
        if isinstance(parameter_data, dict):
            return parameter_data.get(key, '')
        
        return ''
    
    def get_secret_json(self, secret_name: str, key: str, fallback_env_var: Optional[str] = None) -> str:
        """
        Get specific key from JSON parameter (backward compatibility method)
        
        Args:
            secret_name: Name of the parameter (legacy secret name will be converted)
            key: Key within the JSON parameter
            fallback_env_var: Environment variable name to use as fallback
            
        Returns:
            Parameter value or empty string if not found
        """
        return self.get_parameter_json(secret_name, key, fallback_env_var)
    
    def clear_cache(self):
        """Clear the parameters cache"""
        self._cache.clear()


# Global instance for use across the application
parameter_store_manager = ParameterStoreManager()

# Backward compatibility alias
secrets_manager = parameter_store_manager


def get_openai_api_key() -> str:
    """Get OpenAI API key from SSM Parameter Store or environment"""
    return parameter_store_manager.get_parameter_string(
        'lambda/openai-api-key',  # Will be converted to /ezychat/document-ingestion/openai/api-key
        'OPENAI_API_KEY'
    )


def get_supabase_url() -> str:
    """Get Supabase URL from SSM Parameter Store or environment"""
    return parameter_store_manager.get_parameter_string(
        'lambda/supabase-url',  # Will be converted to /ezychat/document-ingestion/supabase/url
        'SUPABASE_URL'
    )


def get_supabase_service_role_key() -> str:
    """Get Supabase service role key from SSM Parameter Store or environment"""
    return parameter_store_manager.get_parameter_string(
        'lambda/supabase-service-role-key',  # Will be converted to /ezychat/document-ingestion/supabase/service-role-key
        'SUPABASE_SERVICE_ROLE_KEY'
    )


def get_supabase_anon_key() -> str:
    """Get Supabase anonymous key from SSM Parameter Store or environment"""
    return parameter_store_manager.get_parameter_string(
        'lambda/supabase-anon-key',  # Will be converted to /ezychat/document-ingestion/supabase/anon-key
        'SUPABASE_ANON_KEY'
    )


def get_all_parameters() -> Dict[str, str]:
    """Get all parameters for debugging (without sensitive values)"""
    return {
        'has_openai_key': bool(get_openai_api_key()),
        'has_supabase_url': bool(get_supabase_url()),
        'has_supabase_service_key': bool(get_supabase_service_role_key()),
        'has_supabase_anon_key': bool(get_supabase_anon_key()),
        'is_aws_environment': parameter_store_manager._is_aws_environment,
        'parameters_cached': len(parameter_store_manager._cache)
    }


def get_all_secrets() -> Dict[str, str]:
    """Get all parameters for debugging (backward compatibility function)"""
    return get_all_parameters()
