"""
Lambda Handler for Document Ingestion Pipeline

This module provides the presentation layer Lambda handler that orchestrates
the document processing use case using clean architecture principles.
"""

import json
import time
from typing import Dict, Any
from urllib.parse import unquote_plus

from dependency_injector.wiring import Provide, inject

from ..application.use_cases import ProcessDocumentUseCase, ProcessDocumentCommand
from ..infrastructure.dependency_injection import ApplicationContainer


class LambdaHandler:
    """Clean architecture Lambda handler with dependency injection"""

    @inject
    def __init__(self,
                 use_case: ProcessDocumentUseCase = Provide[ApplicationContainer.process_document_use_case],
                 logger = Provide[ApplicationContainer.logger],
                 metrics = Provide[ApplicationContainer.metrics_collector]):
        self._use_case = use_case
        self._logger = logger
        self._metrics = metrics
    
    async def handle(self, event: Dict[str, Any], context: Any) -> Dict[str, Any]:
        """
        Main Lambda handler entry point
        
        Args:
            event: Lambda event (S3 or API Gateway)
            context: Lambda context
            
        Returns:
            Lambda response
        """
        start_time = time.time()
        
        try:
            # Setup logging context
            self._setup_logging_context(context)
            
            self._logger.info(
                "Lambda function invoked",
                operation="lambda_handler",
                status="started",
                event_type="api_gateway" if "httpMethod" in event else "s3_event",
                metrics={"record_count": len(event.get("Records", [])) if "Records" in event else 1}
            )
            
            # Route to appropriate handler
            if "httpMethod" in event:
                return await self._handle_api_gateway_request(event, context, start_time)
            else:
                return await self._handle_s3_event(event, context, start_time)
                
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            self._logger.error(
                "Lambda function failed",
                error=e,
                operation="lambda_handler",
                status="failed",
                duration_ms=duration_ms
            )
            
            self._metrics.record_error("lambda_handler_error")
            
            return {
                "statusCode": 500,
                "body": json.dumps({
                    "error": str(e),
                    "message": "Processing failed"
                })
            }
    
    async def _handle_api_gateway_request(self, event: Dict[str, Any],
                                        context: Any, start_time: float) -> Dict[str, Any]:
        """Handle API Gateway requests (health checks, testing)"""
        duration_ms = (time.time() - start_time) * 1000
        path = event.get("path", "")
        http_method = event.get("httpMethod", "")

        self._logger.info(
            "API Gateway request handled",
            operation="api_gateway_handler",
            status="completed",
            duration_ms=duration_ms,
            path=path,
            http_method=http_method
        )

        # Handle health check endpoint
        if path in ["/document-ingestion/health", "/document-ingestion"]:
            return {
                "statusCode": 200,
                "headers": {
                    "Content-Type": "application/json",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
                },
                "body": json.dumps({
                    "message": "Document ingestion Lambda is running",
                    "status": "healthy",
                    "timestamp": time.time(),
                    "path": path,
                    "method": http_method,
                    "event_type": "api_gateway",
                    "service": "document-ingestion",
                    "version": "1.0.0",
                    "architecture": "clean_architecture"
                })
            }

        # Handle other API Gateway requests (future endpoints)
        return {
            "statusCode": 404,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
                "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
            },
            "body": json.dumps({
                "error": "Not Found",
                "message": f"Endpoint {path} not found",
                "path": path,
                "method": http_method,
                "service": "document-ingestion",
                "available_endpoints": [
                    "/document-ingestion/health",
                    "/document-ingestion"
                ]
            })
        }
    
    async def _handle_s3_event(self, event: Dict[str, Any], 
                             context: Any, start_time: float) -> Dict[str, Any]:
        """Handle S3 event notifications"""
        results = []
        
        # Process each S3 record
        for record in event.get("Records", []):
            if record.get("eventSource") == "aws:s3":
                result = await self._process_s3_record(record)
                results.append(result)
        
        duration_ms = (time.time() - start_time) * 1000
        
        self._logger.info(
            "S3 event processing completed",
            operation="s3_event_handler",
            status="completed",
            duration_ms=duration_ms,
            metrics={
                "processed_records": len(results),
                "successful_records": sum(1 for r in results if r.get("success", False))
            }
        )
        
        return {
            "statusCode": 200,
            "body": json.dumps({
                "message": "S3 processing completed",
                "results": results,
                "architecture": "clean_architecture"
            })
        }
    
    async def _process_s3_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single S3 record"""
        try:
            # Extract S3 information
            bucket = record["s3"]["bucket"]["name"]
            key = unquote_plus(record["s3"]["object"]["key"])
            
            self._logger.info(f"Processing S3 object: s3://{bucket}/{key}")
            
            # Create command and execute use case
            command = ProcessDocumentCommand(bucket=bucket, key=key)
            result = await self._use_case.execute(command)
            
            if result.success:
                return {
                    "success": True,
                    "user_id": result.user_id,
                    "document_id": result.document_id,
                    "processed_rows": result.processed_rows,
                    "processing_time_ms": result.processing_time_ms,
                    "message": "Document processed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": result.error_message,
                    "processing_time_ms": result.processing_time_ms,
                    "message": "Document processing failed"
                }
                
        except Exception as e:
            self._logger.error(f"Error processing S3 record: {e}", error=e)
            return {
                "success": False,
                "error": str(e),
                "message": "Document processing failed"
            }
    
    def _setup_logging_context(self, context: Any) -> None:
        """Setup logging context with Lambda information"""
        if context:
            # This would be implemented based on the logging infrastructure
            # For now, we'll just log the context information
            self._logger.debug(
                "Lambda context setup",
                aws_request_id=getattr(context, "aws_request_id", None),
                function_name=getattr(context, "function_name", None),
                remaining_time_ms=getattr(context, "get_remaining_time_in_millis", lambda: 0)()
            )


# Global container and handler instances for Lambda
_container = None
_handler = None


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda entry point

    This function creates a handler instance and delegates to the clean architecture handler.
    """
    global _container, _handler

    # Initialize container and wire dependencies if not exists (Lambda container reuse)
    if _container is None:
        _container = ApplicationContainer()
        _container.wire(modules=[__name__])

    if _handler is None:
        _handler = LambdaHandler()

    # Since Lambda doesn't support async handlers directly, we need to run the async handler
    import asyncio

    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(_handler.handle(event, context))
