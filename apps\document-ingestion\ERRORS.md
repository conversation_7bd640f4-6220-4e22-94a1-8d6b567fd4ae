=========================== short test summary info ============================
FAILED tests/integration/test_lambda_handler.py::TestLambdaHandlerIntegration::test_lambda_handler_api_gateway - assert 404 == 200
FAILED tests/integration/test_lambda_handler.py::TestLambdaHandlerIntegration::test_lambda_handler_exception - assert 200 == 500
FAILED tests/integration/test_lambda_handler.py::TestLambdaHandlerIntegration::test_container_singleton_behavior_in_lambda - assert 404 == 200
FAILED tests/integration/test_lambda_handler.py::TestLambdaHandlerClass::test_handle_api_gateway_request - assert 404 == 200
FAILED tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_embedding_generation_error - AttributeError: Mock object has no attribute 'generate_product_embeddings'. Did you mean: 'generate_embeddings'?
FAILED tests/unit/domain/test_services.py::TestEmbeddingGenerationService::test_generate_product_embeddings - Failed: async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted
FAILED tests/unit/domain/test_services.py::TestEmbeddingGenerationService::test_generate_product_embeddings_empty_list - Failed: async def functions are not natively supported.
You need to install a suitable plugin for your async framework, for example:
  - anyio
  - pytest-asyncio
  - pytest-tornasync
  - pytest-trio
  - pytest-twisted

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_success - AttributeError: Mock object has no attribute 'generate_product_embeddings'. Did you mean: 'generate_embeddings'?

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_success - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005f988c0> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_invalid_s3_key - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f30060c7290> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_unsupported_file_format - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005f9b290> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_tenant_access_violation

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_tenant_access_violation - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005f99fa0> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_file_too_large

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_file_too_large - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005ed3fb0> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_csv_parsing_error - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f30060c52e0> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_too_many_rows

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_too_many_rows - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005eb8d40> is not overridden

ERROR tests/unit/application/test_use_cases.py::TestProcessDocumentUseCase::test_execute_embedding_generation_error - dependency_injector.errors.Error: Container <dependency_injector.containers.DynamicContainer object at 0x7f3005eba120> is not overridden