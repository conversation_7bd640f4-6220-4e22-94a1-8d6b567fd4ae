"""
Unit tests for domain services

These tests verify the business logic of domain services
without any external dependencies.
"""

import pytest
from unittest.mock import Mock

from src.domain.services import (
    SearchableTextGenerator, EmbeddingGenerationService,
    DocumentValidationService, MultiTenantSecurityService
)
from src.domain.entities import ProductData, EmbeddingVector
from src.domain.interfaces import IEmbeddingService, ILogger


class TestSearchableTextGenerator:
    """Test SearchableTextGenerator domain service"""
    
    def test_generate_with_default_fields(self):
        """Test generating searchable text with default fields"""
        generator = SearchableTextGenerator()
        
        product_data = ProductData(
            data={
                "name": "Test Product",
                "description": "A great product",
                "price": "29.99",
                "category": "Electronics"
            },
            row_index=0
        )
        
        text = generator.generate(product_data)
        
        assert "Name: Test Product" in text
        assert "Description: A great product" in text
        assert "Category: Electronics" in text
        # Price is not in default search fields
        assert "price" not in text.lower()
    
    def test_generate_with_custom_fields(self):
        """Test generating searchable text with custom fields"""
        custom_fields = ["name", "price"]
        generator = SearchableTextGenerator(search_fields=custom_fields)
        
        product_data = ProductData(
            data={
                "name": "Test Product",
                "description": "A great product",
                "price": "29.99"
            },
            row_index=0
        )
        
        text = generator.generate(product_data)
        
        assert "Name: Test Product" in text
        assert "Price: 29.99" in text
        assert "description" not in text.lower()
    
    def test_generate_no_matching_fields(self):
        """Test generating text when no fields match"""
        generator = SearchableTextGenerator(search_fields=["nonexistent"])
        
        product_data = ProductData(
            data={"custom_field": "Custom Value"},
            row_index=0
        )
        
        text = generator.generate(product_data)
        
        # Should fall back to all string fields
        assert "Custom Field: Custom Value" in text


class TestEmbeddingGenerationService:
    """Test EmbeddingGenerationService domain service"""

    @pytest.mark.asyncio
    async def test_generate_product_embeddings(self):
        """Test generating embeddings for products"""
        # Setup mocks
        mock_embedding_service = Mock(spec=IEmbeddingService)
        mock_text_generator = Mock(spec=SearchableTextGenerator)
        mock_logger = Mock(spec=ILogger)

        # Configure mocks
        mock_text_generator.generate.side_effect = [
            "Product 1 text",
            "Product 2 text"
        ]

        mock_embeddings = [
            EmbeddingVector([0.1, 0.2], 2, "test-model"),
            EmbeddingVector([0.3, 0.4], 2, "test-model")
        ]
        # Make the async method return a coroutine
        async def mock_generate_embeddings(texts):
            return mock_embeddings
        mock_embedding_service.generate_embeddings = mock_generate_embeddings

        # Create service
        service = EmbeddingGenerationService(
            embedding_service=mock_embedding_service,
            text_generator=mock_text_generator,
            logger=mock_logger
        )

        # Test data
        products = [
            ProductData({"name": "Product 1"}, 0),
            ProductData({"name": "Product 2"}, 1)
        ]

        # Execute
        result = await service.generate_product_embeddings(products)

        # Verify
        assert len(result) == 2
        assert result[0]['row_index'] == 0
        assert result[0]['searchable_text'] == "Product 1 text"
        assert result[0]['embedding_vector'] == [0.1, 0.2]

        assert result[1]['row_index'] == 1
        assert result[1]['searchable_text'] == "Product 2 text"
        assert result[1]['embedding_vector'] == [0.3, 0.4]

        # Verify calls
        mock_text_generator.generate.assert_any_call(products[0])
        mock_text_generator.generate.assert_any_call(products[1])
    
    @pytest.mark.asyncio
    async def test_generate_product_embeddings_empty_list(self):
        """Test generating embeddings for empty product list"""
        mock_embedding_service = Mock(spec=IEmbeddingService)
        mock_text_generator = Mock(spec=SearchableTextGenerator)
        mock_logger = Mock(spec=ILogger)

        service = EmbeddingGenerationService(
            embedding_service=mock_embedding_service,
            text_generator=mock_text_generator,
            logger=mock_logger
        )

        result = await service.generate_product_embeddings([])

        assert result == []


class TestDocumentValidationService:
    """Test DocumentValidationService domain service"""
    
    def test_validate_file_size_valid(self):
        """Test validating valid file size"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        result = service.validate_file_size(1000, 2000)
        
        assert result is True
        mock_logger.warning.assert_not_called()
    
    def test_validate_file_size_invalid(self):
        """Test validating invalid file size"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        result = service.validate_file_size(2000, 1000)
        
        assert result is False
        mock_logger.warning.assert_called_once()
    
    def test_validate_row_count_valid(self):
        """Test validating valid row count"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        result = service.validate_row_count(500, 1000)
        
        assert result is True
        mock_logger.warning.assert_not_called()
    
    def test_validate_row_count_invalid(self):
        """Test validating invalid row count"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        result = service.validate_row_count(1500, 1000)
        
        assert result is False
        mock_logger.warning.assert_called_once()
    
    def test_validate_column_names_valid(self):
        """Test validating valid column names"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        columns = ["name", "description", "price"]
        result = service.validate_column_names(columns)
        
        assert result is True
        mock_logger.warning.assert_not_called()
    
    def test_validate_column_names_empty(self):
        """Test validating empty column names"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        result = service.validate_column_names([])
        
        assert result is False
        mock_logger.warning.assert_called_once()
    
    def test_validate_column_names_invalid(self):
        """Test validating invalid column names"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        columns = ["valid", "", "  ", "x" * 300]  # Empty, whitespace, too long
        result = service.validate_column_names(columns)
        
        assert result is False
        mock_logger.warning.assert_called_once()
    
    def test_calculate_data_quality_score(self):
        """Test calculating data quality score"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        products = [
            ProductData({"name": "Product 1", "price": "29.99", "desc": ""}, 0),
            ProductData({"name": "Product 2", "price": None, "desc": "Good"}, 1)
        ]
        
        # 4 non-empty fields out of 6 total = 0.667
        score = service.calculate_data_quality_score(products)
        
        assert abs(score - 0.6667) < 0.001
    
    def test_calculate_data_quality_score_empty(self):
        """Test calculating data quality score for empty list"""
        mock_logger = Mock(spec=ILogger)
        service = DocumentValidationService(mock_logger)
        
        score = service.calculate_data_quality_score([])
        
        assert score == 0.0


class TestMultiTenantSecurityService:
    """Test MultiTenantSecurityService domain service"""
    
    def test_validate_tenant_access_valid(self):
        """Test validating valid tenant access"""
        mock_logger = Mock(spec=ILogger)
        service = MultiTenantSecurityService(mock_logger)
        
        result = service.validate_tenant_access("user123", "user123/uploaded/file.csv")
        
        assert result is True
        mock_logger.error.assert_not_called()
    
    def test_validate_tenant_access_invalid(self):
        """Test validating invalid tenant access"""
        mock_logger = Mock(spec=ILogger)
        service = MultiTenantSecurityService(mock_logger)
        
        result = service.validate_tenant_access("user123", "user456/uploaded/file.csv")
        
        assert result is False
        mock_logger.error.assert_called_once()
    
    def test_sanitize_user_id_clean(self):
        """Test sanitizing clean user ID"""
        mock_logger = Mock(spec=ILogger)
        service = MultiTenantSecurityService(mock_logger)
        
        result = service.sanitize_user_id("user123")
        
        assert result == "user123"
        mock_logger.warning.assert_not_called()
    
    def test_sanitize_user_id_dirty(self):
        """Test sanitizing dirty user ID"""
        mock_logger = Mock(spec=ILogger)
        service = MultiTenantSecurityService(mock_logger)
        
        result = service.sanitize_user_id("user@123!#$")
        
        assert result == "user123"
        mock_logger.warning.assert_called_once()
